---
import MainLayout from '../../layouts/MainLayout.astro';
import { createServerSupabaseClient } from '../../utils/supabase/server';
import { OptimizedDealCard } from '../../components/DesignSystem/Card/OptimizedDealCard';
import { CouponActionButton } from '../../components/CouponPage/CouponActionButton';
import { CouponVotingButton } from '../../components/CouponPage/CouponVotingButton';
import { CouponBookmarkButton } from '../../components/CouponPage/CouponBookmarkButton';
import { CouponPagePopup } from '../../components/CouponPage/CouponPagePopup';
import CouponActionButtons from '../../components/CouponPage/CouponActionButtons';
import UsersAlsoLiked from '../../components/CouponPage/UsersAlsoLiked';
import RelatedKeywords from '../../components/CouponPage/RelatedKeywords';

// Get the coupon slug from the URL
const { slug } = Astro.params;

if (!slug) {
  return Astro.redirect('/coupons', 302);
}

// Create server-side Supabase client
const supabase = createServerSupabaseClient({
  cookies: () => Astro.request.headers.get('cookie') || ''
});

// Try to find deal by slug field (simple and direct)
let deal = null;
let dealError = null;

// First try by the slug field (this should work for most cases)
const { data: dealBySlug, error: slugError } = await supabase
  .from('deals')
  .select(`
    *,
    merchants:merchant_id (name, website_url, logo_url),
    brands:brand_id (name, logo_url, slug),
    categories:category_id (name, slug)
  `)
  .eq('slug', slug)
  .single();

if (dealBySlug) {
  deal = dealBySlug;
} else {
  // Fallback: Try by ID if slug is numeric
  if (!isNaN(Number(slug))) {
    const { data: dealById, error: idError } = await supabase
      .from('deals')
      .select(`
        *,
        merchants:merchant_id (name, website_url, logo_url),
        brands:brand_id (name, logo_url, slug),
        categories:category_id (name, slug)
      `)
      .eq('id', parseInt(slug))
      .single();

    if (dealById) {
      deal = dealById;
    } else {
      dealError = idError || slugError;
    }
  } else {
    dealError = slugError;
  }
}

if (!deal) {
  console.error('Coupon not found for slug:', slug);

  // Last resort: try to extract ID from slug and redirect to deal page
  const possibleId = slug.match(/\d+$/)?.[0]; // Extract trailing numbers
  if (possibleId) {
    console.log('Attempting fallback redirect to deal ID:', possibleId);
    return Astro.redirect(`/deal/${possibleId}`, 302);
  }

  // If all else fails, redirect to coupons page
  return Astro.redirect('/coupons', 302);
}

// Check if deal is expired
const now = new Date();
const isExpired = deal.deal_end_date && new Date(deal.deal_end_date) < now;

// Get related coupons from same brand or category
const { data: relatedCoupons } = await supabase
  .from('deals')
  .select(`
    *,
    merchants:merchant_id (name, website_url, logo_url),
    brands:brand_id (name, logo_url, slug),
    categories:category_id (name, slug)
  `)
  .or(`brand_id.eq.${deal.brand_id},category_id.eq.${deal.category_id}`)
  .neq('id', deal.id)
  .gt('deal_end_date', now.toISOString())
  .limit(8);

// Enhanced SEO metadata for individual coupons
const title = `${deal.title} - ${deal.discount} Coupon Code | VapeHybrid`;
const description = `Get ${deal.discount} off with this verified ${deal.title} coupon code. ${deal.description || 'Save on premium vape products'} - Updated daily with real-time verification.`;

// Structured data for individual coupon
const structuredData = {
  '@context': 'https://schema.org',
  '@type': 'Offer',
  name: deal.title,
  description: deal.description || `Save ${deal.discount} on ${deal.title}`,
  url: Astro.url.href,
  image: deal.imagebig_url || deal.image_url,
  price: deal.price || undefined,
  priceCurrency: 'USD',
  availability: isExpired ? 'https://schema.org/OutOfStock' : 'https://schema.org/InStock',
  validThrough: deal.deal_end_date,
  priceValidUntil: deal.deal_end_date,
  brand: deal.brands ? {
    '@type': 'Brand',
    name: deal.brands.name
  } : undefined,
  seller: {
    '@type': 'Organization',
    name: deal.merchants?.name || 'VapeHybrid Partner',
    url: deal.merchants?.website_url
  },
  category: deal.categories?.name
};

// Calculate time remaining
const timeRemaining = deal.deal_end_date ? Math.max(0, new Date(deal.deal_end_date).getTime() - now.getTime()) : 0;
const daysRemaining = Math.floor(timeRemaining / (1000 * 60 * 60 * 24));
const hoursRemaining = Math.floor((timeRemaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
---

<MainLayout title={title} description={description} structuredData={structuredData}>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6 pb-12">
    <!-- Breadcrumbs - Aligned with main content -->
    <div class="max-w-6xl mx-auto">
      <nav class="mb-6" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-1 text-xs text-design-muted-foreground">
          <li><a href="/" class="hover:text-design-foreground transition-colors">Home</a></li>
          <li><span class="mx-1">/</span></li>
          <li><a href="/coupons" class="hover:text-design-foreground transition-colors">Coupons</a></li>
          {deal.brands && (
            <>
              <li><span class="mx-1">/</span></li>
              <li><a href={`/coupons/brands/${deal.brands.slug}`} class="hover:text-design-foreground transition-colors">{deal.brands.name}</a></li>
            </>
          )}
          <li><span class="mx-1">/</span></li>
          <li class="text-design-foreground font-medium">{deal.title}</li>
        </ol>
      </nav>
    </div>

    {/* Expired Deal Notice */}
    {isExpired && (
      <div class="mb-6 p-4 bg-design-destructive/10 border border-design-destructive/20 rounded-lg text-center">
        <div class="flex items-center justify-center">
          <svg class="w-5 h-5 text-design-destructive mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          <span class="text-design-destructive font-medium">This coupon has expired</span>
        </div>
      </div>
    )}

    <!-- Redesigned Layout: 2/3 Left + 1/3 Right -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
      <!-- Left Column (2/3 Width) - Main Content -->
      <div class="lg:col-span-2 space-y-6">
        <!-- Enhanced Coupon Header -->
        <div class="bg-design-card border border-design-border rounded-xl p-6">
          <!-- Title (H1) - Short and Rich -->
          <h1 class="text-3xl md:text-4xl font-bold text-design-foreground mb-4 leading-tight">
            {deal.title}
          </h1>

          <!-- Merchant, Brand, Category Info -->
          <div class="flex flex-wrap gap-4 text-sm mb-6">
            {deal.merchants && (
              <div class="flex items-center bg-design-muted/10 px-3 py-1 rounded-full">
                <span class="font-medium text-design-muted-foreground">Store:</span>
                <a href={`/coupons/merchants/${deal.merchants.slug || deal.merchants.id}`} class="ml-1 text-primary hover:underline">
                  {deal.merchants.name}
                </a>
              </div>
            )}
            {deal.brands && (
              <div class="flex items-center bg-design-muted/10 px-3 py-1 rounded-full">
                <span class="font-medium text-design-muted-foreground">Brand:</span>
                <a href={`/coupons/brands/${deal.brands.slug}`} class="ml-1 text-primary hover:underline">
                  {deal.brands.name}
                </a>
              </div>
            )}
            {deal.categories && (
              <div class="flex items-center bg-design-muted/10 px-3 py-1 rounded-full">
                <span class="font-medium text-design-muted-foreground">Category:</span>
                <a href={`/coupons/categories/${deal.categories.slug}`} class="ml-1 text-primary hover:underline">
                  {deal.categories.name}
                </a>
              </div>
            )}
          </div>

          <!-- Price Information Table -->
          {deal.price && deal.discount && (
            <div class="bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-lg p-6 mb-6">
              <h3 class="text-lg font-semibold text-design-foreground mb-4">Price Breakdown</h3>
              <div class="space-y-3">
                {(() => {
                  // Parse discount percentage
                  const discountStr = deal.discount.toString();
                  const discountMatch = discountStr.match(/(\d+(?:\.\d+)?)/);
                  const discountPercent = discountMatch ? parseFloat(discountMatch[1]) : 0;

                  // Calculate prices
                  const finalPrice = parseFloat(deal.price.toString());
                  const originalPrice = discountPercent > 0 ? finalPrice / (1 - discountPercent / 100) : finalPrice;
                  const discountAmount = originalPrice - finalPrice;

                  return (
                    <div class="grid grid-cols-2 gap-4 text-sm">
                      <div class="flex justify-between py-2 border-b border-primary/20">
                        <span class="text-design-muted-foreground">Original Price:</span>
                        <span class="font-semibold text-design-foreground">${originalPrice.toFixed(2)}</span>
                      </div>
                      <div class="flex justify-between py-2 border-b border-primary/20">
                        <span class="text-design-muted-foreground">Discount:</span>
                        <span class="font-semibold text-primary">{deal.discount} (-${discountAmount.toFixed(2)})</span>
                      </div>
                      <div class="flex justify-between py-2 border-b border-primary/20 col-span-2">
                        <span class="text-design-muted-foreground font-medium">Final Price:</span>
                        <span class="font-bold text-lg text-green-600">${finalPrice.toFixed(2)}</span>
                      </div>
                    </div>
                  );
                })()}
              </div>
            </div>
          )}

          {/* Fallback for deals without price info */}
          {(!deal.price || !deal.discount) && deal.discount && (
            <div class="bg-gradient-to-r from-primary/10 to-primary/5 border border-primary/20 rounded-lg p-4 mb-6">
              <div class="text-center">
                <div class="text-3xl font-bold text-primary mb-2">{deal.discount}</div>
                <div class="text-sm text-design-muted-foreground">Discount Available</div>
              </div>
            </div>
          )}

          <!-- Countdown Timer -->
          {deal.deal_end_date && !isExpired && (
            <div class="bg-design-muted/5 border border-design-border rounded-lg p-4 mb-6">
              <div class="flex items-center justify-center">
                <svg class="w-5 h-5 text-design-muted-foreground mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />
                </svg>
                <span class="text-design-foreground font-medium">
                  Expires in {daysRemaining > 0 ? `${daysRemaining} days` : `${hoursRemaining} hours`}
                </span>
              </div>
            </div>
          )}
        </div>

        <!-- Contextual Description -->
        {deal.description && (
          <div class="bg-design-card border border-design-border rounded-xl p-6">
            <h2 class="text-xl font-semibold text-design-foreground mb-4">About This Deal</h2>
            <p class="text-design-muted-foreground leading-relaxed text-base">{deal.description}</p>
          </div>
        )}

        <!-- How to Use Section -->
        <div class="bg-design-card border border-design-border rounded-xl p-6">
          <h2 class="text-xl font-semibold text-design-foreground mb-4">How to Redeem</h2>
          <div class="space-y-3">
            <div class="flex items-start">
              <div class="flex-shrink-0 w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</div>
              <p class="text-design-muted-foreground">Click the "Get Coupon" button to reveal the code</p>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0 w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</div>
              <p class="text-design-muted-foreground">Copy the coupon code and visit the merchant's website</p>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0 w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</div>
              <p class="text-design-muted-foreground">Add your favorite vape products to cart</p>
            </div>
            <div class="flex items-start">
              <div class="flex-shrink-0 w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold mr-3 mt-0.5">4</div>
              <p class="text-design-muted-foreground">Apply the coupon code at checkout and save!</p>
            </div>
          </div>
        </div>

        <!-- Users Also Liked Section - Dynamic -->
        <UsersAlsoLiked
          currentDealId={deal.id}
          merchantId={deal.merchant_id}
          brandId={deal.brand_id}
          categoryId={deal.category_id}
          client:load
        />

        <!-- Related Keywords Section - Dynamic -->
        <RelatedKeywords
          deal={deal}
          client:load
        />
      </div>

      <!-- Right Column (1/3 Width) - Sidebar -->
      <div class="lg:col-span-1 space-y-6">
        <!-- Coupon Image & CTA Card -->
        <div class="bg-design-card border border-design-border rounded-xl p-6 sticky top-6">
          <!-- Coupon Image (Fallback if null) -->
          <div class="flex justify-center mb-6">
            <div class="w-24 h-24 rounded-lg bg-design-muted/10 flex items-center justify-center overflow-hidden">
              {deal.imagebig_url || deal.image_url ? (
                <img
                  src={deal.imagebig_url || deal.image_url}
                  alt={deal.title}
                  class="w-full h-full object-cover rounded-lg"
                  loading="eager"
                />
              ) : (
                <div class="w-full h-full bg-primary/10 flex items-center justify-center">
                  <span class="text-primary font-bold text-2xl">{deal.title.charAt(0)}</span>
                </div>
              )}
            </div>
          </div>

          <!-- Verified Badge & Success Info -->
          <div class="text-center mb-6">
            <div class="inline-flex items-center bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mb-2">
              <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
              </svg>
              Verified Today
            </div>
            <div class="text-sm text-design-muted-foreground">
              98% Success Rate • Used {deal.click_count || 0} times
            </div>
          </div>

          <!-- CTA Buttons -->
          <div class="space-y-3">
            <CouponActionButton deal={deal} isExpired={isExpired} client:load />

            <!-- Action Buttons -->
            <CouponActionButtons
              dealId={deal.id}
              dealTitle={deal.title}
              dealUrl={`${Astro.url.origin}/coupon/${deal.slug || deal.id}`}
              client:load
            />
          </div>
        </div>

        <!-- Last Used Info -->
        <div class="bg-design-card border border-design-border rounded-xl p-4">
          <div class="text-center">
            <div class="text-sm text-design-muted-foreground mb-1">Last Used</div>
            <div class="font-semibold text-design-foreground">2 hours ago</div>
            <div class="text-xs text-design-muted-foreground mt-1">by VapeEnthusiast92</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Related Coupons -->
    {relatedCoupons && relatedCoupons.length > 0 && (
      <div class="mt-12">
        <h3 class="text-2xl font-bold text-design-foreground mb-8 text-center">Related Coupons</h3>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {relatedCoupons.map((relatedCoupon) => (
            <OptimizedDealCard
              deal={relatedCoupon}
              viewMode="grid"
              client:load
            />
          ))}
        </div>
      </div>
    )}
  </div>

  <!-- Popup Component for Modal Display -->
  <CouponPagePopup deal={deal} client:load />
</MainLayout>
