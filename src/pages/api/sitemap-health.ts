/**
 * Sitemap Health Check API Endpoint
 * 
 * Monitors sitemap validity and provides status information
 * GET /api/sitemap-health
 */

import type { APIRoute } from 'astro';
import { readFileSync, existsSync, statSync } from 'fs';
import { join } from 'path';

interface SitemapStatus {
  name: string;
  exists: boolean;
  size: number;
  lastModified: string;
  entryCount?: number;
  valid: boolean;
  error?: string;
}

export const GET: APIRoute = async () => {
  try {
    const publicDir = './public';
    const sitemaps = [
      'sitemap.xml',
      'sitemap-static-pages.xml',
      'sitemap-coupons.xml',
      'sitemap-brands.xml',
      'sitemap-merchants.xml',
      'sitemap-categories.xml'
    ];

    const results: SitemapStatus[] = [];
    let overallHealth = true;

    for (const sitemapName of sitemaps) {
      const filePath = join(publicDir, sitemapName);
      const status: SitemapStatus = {
        name: sitemapName,
        exists: false,
        size: 0,
        lastModified: '',
        valid: false
      };

      try {
        // Check if file exists
        if (existsSync(filePath)) {
          status.exists = true;
          
          // Get file stats
          const stats = statSync(filePath);
          status.size = stats.size;
          status.lastModified = stats.mtime.toISOString();

          // Read and validate XML
          const content = readFileSync(filePath, 'utf-8');
          
          // Basic XML validation
          if (content.includes('<?xml') && 
              (content.includes('<sitemapindex') || content.includes('<urlset'))) {
            status.valid = true;
            
            // Count entries
            if (sitemapName === 'sitemap.xml') {
              const sitemapMatches = content.match(/<sitemap>/g);
              status.entryCount = sitemapMatches ? sitemapMatches.length : 0;
            } else {
              const urlMatches = content.match(/<url>/g);
              status.entryCount = urlMatches ? urlMatches.length : 0;
            }
          } else {
            status.valid = false;
            status.error = 'Invalid XML structure';
            overallHealth = false;
          }
        } else {
          status.error = 'File not found';
          overallHealth = false;
        }
      } catch (error) {
        status.error = error instanceof Error ? error.message : 'Unknown error';
        overallHealth = false;
      }

      results.push(status);
    }

    // Calculate total entries across all sitemaps
    const totalEntries = results
      .filter(r => r.name !== 'sitemap.xml' && r.entryCount)
      .reduce((sum, r) => sum + (r.entryCount || 0), 0);

    const response = {
      status: overallHealth ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      sitemaps: results,
      summary: {
        totalSitemaps: results.length,
        validSitemaps: results.filter(r => r.valid).length,
        totalEntries: totalEntries,
        lastGenerated: results
          .filter(r => r.exists)
          .sort((a, b) => new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime())[0]?.lastModified
      }
    };

    return new Response(JSON.stringify(response, null, 2), {
      status: overallHealth ? 200 : 500,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    });

  } catch (error) {
    console.error('Sitemap health check error:', error);
    
    return new Response(JSON.stringify({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    }, null, 2), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
