/**
 * SEO Validator Vite Plugin
 * 
 * Validates SEO metadata during build process
 */

import { extractSEOFromCode, validateSEO } from '../utils/seo-validator.ts';

export function seoValidatorPlugin() {
  return {
    name: 'seo-validator',
    transform(code, id) {
      // Only validate page files
      if (!id.includes('/pages/') || id.includes('/api/')) {
        return null;
      }

      // Skip non-page files
      if (!id.endsWith('.astro') && !id.endsWith('.tsx')) {
        return null;
      }

      try {
        const seoData = extractSEOFromCode(code);
        
        if (seoData) {
          const result = validateSEO(seoData);
          
          if (!result.isValid) {
            console.error(`❌ SEO Validation Failed: ${id}`);
            result.errors.forEach(error => console.error(`   Error: ${error}`));
            
            // In development, just warn. In production, could fail build
            if (process.env.NODE_ENV === 'production') {
              throw new Error(`SEO validation failed for ${id}: ${result.errors.join(', ')}`);
            }
          }
          
          if (result.warnings.length > 0) {
            console.warn(`⚠️  SEO Warnings: ${id}`);
            result.warnings.forEach(warning => console.warn(`   Warning: ${warning}`));
          }
          
          if (result.isValid && result.warnings.length === 0) {
            console.log(`✅ SEO Valid: ${id.split('/').pop()}`);
          }
        }
      } catch (error) {
        console.warn(`Failed to validate SEO for ${id}:`, error.message);
      }

      return null; // Don't transform the code
    }
  };
}
