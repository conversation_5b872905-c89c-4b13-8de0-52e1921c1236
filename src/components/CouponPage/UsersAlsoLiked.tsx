/**
 * UsersAlsoLiked Component
 * 
 * React component that fetches and displays related deals from Supabase
 * All items are clickable and link to actual coupon pages
 */

import React, { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';

interface Deal {
  id: number;
  title: string;
  slug?: string;
  discount?: string;
  merchants?: {
    name: string;
    slug?: string;
  };
  brands?: {
    name: string;
    slug?: string;
  };
}

interface UsersAlsoLikedProps {
  currentDealId: number;
  merchantId?: number;
  brandId?: number;
  categoryId?: number;
}

export const UsersAlsoLiked: React.FC<UsersAlsoLikedProps> = ({
  currentDealId,
  merchantId,
  brandId,
  categoryId
}) => {
  const [relatedDeals, setRelatedDeals] = useState<Deal[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRelatedDeals();
  }, [currentDealId, merchantId, brandId, categoryId]);

  const fetchRelatedDeals = async () => {
    try {
      const supabaseUrl = import.meta.env.PUBLIC_SUPABASE_URL || 'https://zlnvivfgzgcjuspktadj.supabase.co';
      const supabaseKey = import.meta.env.PUBLIC_SUPABASE_ANON_KEY;
      
      if (!supabaseKey) {
        console.error('Supabase key not found');
        setLoading(false);
        return;
      }

      const supabase = createClient(supabaseUrl, supabaseKey);

      // Build query to find related deals
      let query = supabase
        .from('deals')
        .select(`
          id,
          title,
          slug,
          discount,
          merchants:merchant_id (name, slug),
          brands:brand_id (name, slug)
        `)
        .neq('id', currentDealId)
        .limit(3);

      // Prioritize deals from same merchant, then brand, then category
      if (merchantId) {
        query = query.eq('merchant_id', merchantId);
      } else if (brandId) {
        query = query.eq('brand_id', brandId);
      } else if (categoryId) {
        query = query.eq('category_id', categoryId);
      }

      const { data: deals, error } = await query;

      if (error) {
        console.error('Error fetching related deals:', error);
        setLoading(false);
        return;
      }

      // If we don't have enough deals, fetch some popular ones
      if (!deals || deals.length < 3) {
        const { data: popularDeals } = await supabase
          .from('deals')
          .select(`
            id,
            title,
            slug,
            discount,
            merchants:merchant_id (name, slug),
            brands:brand_id (name, slug)
          `)
          .neq('id', currentDealId)
          .order('click_count', { ascending: false })
          .limit(3);

        setRelatedDeals(popularDeals || []);
      } else {
        setRelatedDeals(deals);
      }

    } catch (error) {
      console.error('Error in fetchRelatedDeals:', error);
    } finally {
      setLoading(false);
    }
  };

  const getDiscountDisplay = (discount?: string) => {
    if (!discount) return 'Deal';
    
    // Extract percentage or dollar amount
    const percentMatch = discount.match(/(\d+)%/);
    const dollarMatch = discount.match(/\$(\d+)/);
    
    if (percentMatch) return `${percentMatch[1]}%`;
    if (dollarMatch) return `$${dollarMatch[1]}`;
    return discount;
  };

  const getDiscountColor = (discount?: string) => {
    if (!discount) return 'text-primary';
    
    if (discount.includes('Free') || discount.includes('free')) {
      return 'text-green-600';
    }
    return 'text-primary';
  };

  const getDealUrl = (deal: Deal) => {
    return `/coupon/${deal.slug || deal.id}`;
  };

  const getMerchantInitial = (deal: Deal) => {
    const name = deal.merchants?.name || deal.brands?.name || deal.title;
    return name.charAt(0).toUpperCase();
  };

  if (loading) {
    return (
      <div className="bg-design-card border border-design-border rounded-xl p-6">
        <h2 className="text-xl font-semibold text-design-foreground mb-4">Users Also Liked</h2>
        <div className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-center justify-between p-3 bg-design-muted/5 rounded-lg animate-pulse">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-design-muted/20 rounded-full"></div>
                <div>
                  <div className="w-32 h-4 bg-design-muted/20 rounded mb-1"></div>
                  <div className="w-20 h-3 bg-design-muted/20 rounded"></div>
                </div>
              </div>
              <div className="w-8 h-4 bg-design-muted/20 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (relatedDeals.length === 0) {
    return null; // Don't show section if no deals
  }

  return (
    <div className="bg-design-card border border-design-border rounded-xl p-6">
      <h2 className="text-xl font-semibold text-design-foreground mb-4">Users Also Liked</h2>
      <div className="space-y-3">
        {relatedDeals.map((deal) => (
          <a
            key={deal.id}
            href={getDealUrl(deal)}
            className="flex items-center justify-between p-3 bg-design-muted/5 rounded-lg hover:bg-design-muted/10 transition-colors group"
          >
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                <span className="text-primary font-bold text-sm">
                  {getMerchantInitial(deal)}
                </span>
              </div>
              <div>
                <div className="text-sm font-medium text-design-foreground group-hover:text-primary transition-colors line-clamp-1">
                  {deal.title}
                </div>
                <div className="text-xs text-design-muted-foreground">
                  {deal.merchants?.name || deal.brands?.name || 'Popular choice'}
                </div>
              </div>
            </div>
            <span className={`text-sm font-medium ${getDiscountColor(deal.discount)}`}>
              {getDiscountDisplay(deal.discount)}
            </span>
          </a>
        ))}
      </div>
    </div>
  );
};

export default UsersAlsoLiked;
