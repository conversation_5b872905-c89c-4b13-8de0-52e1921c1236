/**
 * CouponVotingButton Component
 *
 * React component for voting functionality on individual coupon pages
 * Includes both thumbs up and thumbs down options
 */

import React, { useState, useCallback } from 'react';
import { ThumbsUp, ThumbsDown } from 'lucide-react';

interface CouponVotingButtonProps {
  dealId: string | number;
}

export const CouponVotingButton: React.FC<CouponVotingButtonProps> = ({ dealId }) => {
  const [voteType, setVoteType] = useState<'up' | 'down' | null>(null);

  // Handle voting - both up and down
  const handleVote = useCallback((type: 'up' | 'down') => {
    if (voteType) return; // Already voted

    setVoteType(type);

    // Send tracking beacon
    if (typeof window !== 'undefined' && typeof navigator.sendBeacon === 'function') {
      try {
        navigator.sendBeacon('/api/track-click', JSON.stringify({
          dealId: dealId.toString(),
          action: `vote_${type}`,
          timestamp: Date.now()
        }));
      } catch (error) {
        console.warn('Failed to send vote tracking beacon:', error);
      }
    }
  }, [dealId, voteType]);

  return (
    <div className="flex items-center gap-2 w-full">
      {/* Thumbs Up Button */}
      <button
        onClick={() => handleVote('up')}
        disabled={!!voteType}
        className={`flex items-center justify-center gap-2 px-4 py-2 rounded-lg border transition-colors flex-1 ${
          voteType === 'up'
            ? 'bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 border-green-300 dark:border-green-600 cursor-not-allowed'
            : voteType === 'down'
            ? 'opacity-50 cursor-not-allowed border-design-border text-design-muted-foreground'
            : 'border-design-border hover:bg-design-muted/10 text-design-foreground'
        }`}
      >
        <ThumbsUp size={16} className={voteType === 'up' ? 'fill-current' : ''} />
        <span className="text-sm font-medium">{voteType === 'up' ? 'Thanks!' : 'Helpful'}</span>
      </button>

      {/* Thumbs Down Button */}
      <button
        onClick={() => handleVote('down')}
        disabled={!!voteType}
        className={`flex items-center justify-center gap-2 px-4 py-2 rounded-lg border transition-colors flex-1 ${
          voteType === 'down'
            ? 'bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 border-red-300 dark:border-red-600 cursor-not-allowed'
            : voteType === 'up'
            ? 'opacity-50 cursor-not-allowed border-design-border text-design-muted-foreground'
            : 'border-design-border hover:bg-design-muted/10 text-design-foreground'
        }`}
      >
        <ThumbsDown size={16} className={voteType === 'down' ? 'fill-current' : ''} />
        <span className="text-sm font-medium">{voteType === 'down' ? 'Not Helpful' : 'Not Helpful'}</span>
      </button>
    </div>
  );
};
