/**
 * CouponActionButtons Component
 * 
 * React component for action buttons on individual coupon pages
 * Includes Save, Share, Helpful, Not Helpful buttons with proper functionality
 */

import React, { useState, useCallback } from 'react';
import { Heart, Share2, ThumbsUp, ThumbsDown } from 'lucide-react';
import { toast } from 'sonner';

interface CouponActionButtonsProps {
  dealId: string | number;
  dealTitle: string;
  dealUrl?: string;
}

export const CouponActionButtons: React.FC<CouponActionButtonsProps> = ({ 
  dealId, 
  dealTitle,
  dealUrl 
}) => {
  const [bookmarked, setBookmarked] = useState(false);
  const [voteType, setVoteType] = useState<'up' | 'down' | null>(null);

  // Check if deal is bookmarked on mount
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const bookmarkIds = JSON.parse(localStorage.getItem('bookmarkIds') || '[]');
        setBookmarked(bookmarkIds.includes(dealId.toString()));
      } catch (error) {
        console.error('Error checking bookmark status:', error);
      }
    }
  }, [dealId]);

  // Handle bookmark toggle
  const handleBookmark = useCallback(async () => {
    if (typeof window === 'undefined') return;

    try {
      const bookmarkIds = JSON.parse(localStorage.getItem('bookmarkIds') || '[]');
      const dealIdString = dealId.toString();

      if (bookmarked) {
        // Remove bookmark
        const updatedIds = bookmarkIds.filter((id: string) => id !== dealIdString);
        localStorage.setItem('bookmarkIds', JSON.stringify(updatedIds));
        setBookmarked(false);
        toast.success('Removed from saved coupons');
      } else {
        // Add bookmark
        const updatedIds = [...bookmarkIds, dealIdString];
        localStorage.setItem('bookmarkIds', JSON.stringify(updatedIds));
        setBookmarked(true);
        toast.success('Saved to your coupons');
      }

      // Dispatch events for other components
      window.dispatchEvent(new StorageEvent('storage', {
        key: 'bookmarkIds',
        newValue: localStorage.getItem('bookmarkIds')
      }));

      window.dispatchEvent(new CustomEvent('bookmarkChange', {
        detail: JSON.parse(localStorage.getItem('bookmarkIds') || '[]')
      }));

    } catch (error) {
      console.error('Error toggling bookmark:', error);
      toast.error('Error saving bookmark');
    }
  }, [dealId, bookmarked]);

  // Handle share
  const handleShare = useCallback(async () => {
    const shareUrl = dealUrl || window.location.href;
    const shareText = `Check out this deal: ${dealTitle}`;

    if (navigator.share) {
      try {
        await navigator.share({
          title: dealTitle,
          text: shareText,
          url: shareUrl,
        });
        toast.success('Shared successfully!');
      } catch (error) {
        if (error instanceof Error && error.name !== 'AbortError') {
          console.error('Error sharing:', error);
          fallbackShare(shareUrl);
        }
      }
    } else {
      fallbackShare(shareUrl);
    }
  }, [dealTitle, dealUrl]);

  // Fallback share method
  const fallbackShare = useCallback((url: string) => {
    if (navigator.clipboard) {
      navigator.clipboard.writeText(url).then(() => {
        toast.success('Link copied to clipboard!');
      }).catch(() => {
        toast.error('Failed to copy link');
      });
    } else {
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = url;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        toast.success('Link copied to clipboard!');
      } catch (error) {
        toast.error('Failed to copy link');
      }
      document.body.removeChild(textArea);
    }
  }, []);

  // Handle voting
  const handleVote = useCallback((type: 'up' | 'down') => {
    if (voteType) return; // Already voted

    setVoteType(type);

    // Send tracking beacon
    if (typeof window !== 'undefined' && typeof navigator.sendBeacon === 'function') {
      try {
        navigator.sendBeacon('/api/track-click', JSON.stringify({
          dealId: dealId.toString(),
          action: `vote_${type}`,
          timestamp: Date.now()
        }));
      } catch (error) {
        console.warn('Failed to send vote tracking beacon:', error);
      }
    }

    toast.success(type === 'up' ? 'Thanks for your feedback!' : 'Feedback recorded');
  }, [dealId, voteType]);

  return (
    <div className="space-y-3">
      {/* Save and Share Row */}
      <div className="flex gap-2">
        {/* Save Button */}
        <button
          onClick={handleBookmark}
          className={`flex items-center justify-center gap-2 px-4 py-2 rounded-lg border transition-colors flex-1 ${
            bookmarked
              ? 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 border-red-300 dark:border-red-600'
              : 'border-design-border hover:bg-design-muted/10 text-design-foreground'
          }`}
        >
          <Heart 
            size={16} 
            className={bookmarked ? 'fill-current' : ''} 
          />
          <span className="text-sm font-medium">
            {bookmarked ? 'Saved' : 'Save'}
          </span>
        </button>

        {/* Share Button */}
        <button
          onClick={handleShare}
          className="flex items-center justify-center gap-2 px-4 py-2 rounded-lg border border-design-border hover:bg-design-muted/10 text-design-foreground transition-colors flex-1"
        >
          <Share2 size={16} />
          <span className="text-sm font-medium">Share</span>
        </button>
      </div>

      {/* Voting Row */}
      <div className="flex gap-2">
        {/* Helpful Button */}
        <button
          onClick={() => handleVote('up')}
          disabled={!!voteType}
          className={`flex items-center justify-center gap-2 px-4 py-2 rounded-lg border transition-colors flex-1 ${
            voteType === 'up'
              ? 'bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 border-green-300 dark:border-green-600 cursor-not-allowed'
              : voteType === 'down'
              ? 'opacity-50 cursor-not-allowed border-design-border text-design-muted-foreground'
              : 'border-design-border hover:bg-design-muted/10 text-design-foreground'
          }`}
        >
          <ThumbsUp size={16} className={voteType === 'up' ? 'fill-current' : ''} />
          <span className="text-sm font-medium">
            {voteType === 'up' ? 'Thanks!' : 'Helpful'}
          </span>
        </button>

        {/* Not Helpful Button */}
        <button
          onClick={() => handleVote('down')}
          disabled={!!voteType}
          className={`flex items-center justify-center gap-2 px-4 py-2 rounded-lg border transition-colors flex-1 ${
            voteType === 'down'
              ? 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 border-red-300 dark:border-red-600 cursor-not-allowed'
              : voteType === 'up'
              ? 'opacity-50 cursor-not-allowed border-design-border text-design-muted-foreground'
              : 'border-design-border hover:bg-design-muted/10 text-design-foreground'
          }`}
        >
          <ThumbsDown size={16} className={voteType === 'down' ? 'fill-current' : ''} />
          <span className="text-sm font-medium">Not Helpful</span>
        </button>
      </div>
    </div>
  );
};

export default CouponActionButtons;
