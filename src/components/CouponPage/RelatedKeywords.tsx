/**
 * RelatedKeywords Component
 * 
 * React component that generates clickable keyword tags
 * All keywords link to search results or category/brand pages
 */

import React from 'react';

interface Deal {
  brands?: {
    name: string;
    slug?: string;
  };
  merchants?: {
    name: string;
    slug?: string;
  };
  categories?: {
    name: string;
    slug?: string;
  };
  title: string;
}

interface RelatedKeywordsProps {
  deal: Deal;
}

export const RelatedKeywords: React.FC<RelatedKeywordsProps> = ({ deal }) => {
  
  // Generate dynamic keywords based on deal data
  const generateKeywords = () => {
    const keywords = [];

    // Brand-specific keywords
    if (deal.brands?.name) {
      keywords.push({
        text: `${deal.brands.name.toLowerCase()} coupons`,
        url: `/coupons/brands/${deal.brands.slug || deal.brands.name.toLowerCase().replace(/\s+/g, '-')}`,
        type: 'brand'
      });
      
      keywords.push({
        text: `${deal.brands.name.toLowerCase()} deals`,
        url: `/search?q=${encodeURIComponent(deal.brands.name + ' deals')}`,
        type: 'search'
      });
    }

    // Merchant-specific keywords
    if (deal.merchants?.name) {
      keywords.push({
        text: `${deal.merchants.name.toLowerCase()} deals`,
        url: `/coupons/merchants/${deal.merchants.slug || deal.merchants.name.toLowerCase().replace(/\s+/g, '-')}`,
        type: 'merchant'
      });
      
      keywords.push({
        text: `${deal.merchants.name.toLowerCase()} promo codes`,
        url: `/search?q=${encodeURIComponent(deal.merchants.name + ' promo codes')}`,
        type: 'search'
      });
    }

    // Category-specific keywords
    if (deal.categories?.name) {
      keywords.push({
        text: deal.categories.name.toLowerCase(),
        url: `/coupons/categories/${deal.categories.slug || deal.categories.name.toLowerCase().replace(/\s+/g, '-')}`,
        type: 'category'
      });
    }

    // Extract keywords from title
    const titleKeywords = extractTitleKeywords(deal.title);
    titleKeywords.forEach(keyword => {
      keywords.push({
        text: keyword,
        url: `/search?q=${encodeURIComponent(keyword)}`,
        type: 'search'
      });
    });

    // Generic vape-related keywords
    const genericKeywords = [
      'vape deals',
      'e-liquid discount',
      'vaping savings',
      'mod coupons',
      'juice promo',
      'vape hardware',
      'disposable vapes',
      'pod systems'
    ];

    genericKeywords.forEach(keyword => {
      keywords.push({
        text: keyword,
        url: `/search?q=${encodeURIComponent(keyword)}`,
        type: 'generic'
      });
    });

    // Remove duplicates and limit to 8 keywords
    const uniqueKeywords = keywords.filter((keyword, index, self) => 
      index === self.findIndex(k => k.text === keyword.text)
    ).slice(0, 8);

    return uniqueKeywords;
  };

  // Extract relevant keywords from deal title
  const extractTitleKeywords = (title: string) => {
    const keywords = [];
    const lowerTitle = title.toLowerCase();

    // Common vape product keywords
    const productKeywords = [
      'disposable', 'pod', 'mod', 'tank', 'coil', 'juice', 'liquid',
      'nicotine', 'salt', 'freebase', 'starter kit', 'battery', 'charger'
    ];

    // Discount-related keywords
    const discountKeywords = [
      'free shipping', 'sale', 'clearance', 'bundle', 'kit'
    ];

    // Check for product keywords
    productKeywords.forEach(keyword => {
      if (lowerTitle.includes(keyword)) {
        keywords.push(`${keyword} deals`);
      }
    });

    // Check for discount keywords
    discountKeywords.forEach(keyword => {
      if (lowerTitle.includes(keyword)) {
        keywords.push(keyword);
      }
    });

    return keywords.slice(0, 3); // Limit title-extracted keywords
  };

  const getKeywordStyle = (type: string) => {
    switch (type) {
      case 'brand':
      case 'merchant':
      case 'category':
        return 'bg-primary/10 text-primary hover:bg-primary/20';
      case 'search':
        return 'bg-design-muted/10 text-design-foreground hover:bg-design-muted/20';
      default:
        return 'bg-design-muted/10 text-design-foreground hover:bg-design-muted/20';
    }
  };

  const keywords = generateKeywords();

  if (keywords.length === 0) {
    return null;
  }

  return (
    <div className="bg-design-card border border-design-border rounded-xl p-6">
      <h2 className="text-xl font-semibold text-design-foreground mb-4">Related Keywords</h2>
      <div className="flex flex-wrap gap-2">
        {keywords.map((keyword, index) => (
          <a
            key={index}
            href={keyword.url}
            className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium transition-colors ${getKeywordStyle(keyword.type)}`}
          >
            {keyword.text}
          </a>
        ))}
      </div>
      
      {/* Search suggestion */}
      <div className="mt-4 pt-4 border-t border-design-border">
        <p className="text-xs text-design-muted-foreground mb-2">
          Can't find what you're looking for?
        </p>
        <a
          href="/search"
          className="inline-flex items-center text-sm text-primary hover:text-primary/80 transition-colors"
        >
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          Search all coupons
        </a>
      </div>
    </div>
  );
};

export default RelatedKeywords;
