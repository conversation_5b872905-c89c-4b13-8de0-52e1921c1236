/**
 * UGC (User Generated Content) Block Component
 * 
 * Displays ratings, comments, and reviews for deals
 * Expandable blocks to improve content length for SEO
 */

import React, { useState, useEffect } from 'react';
import { Star, MessageCircle, ThumbsUp, ChevronDown, ChevronUp } from 'lucide-react';

interface Review {
  id: number;
  user_name: string;
  rating: number;
  comment: string;
  created_at: string;
  helpful_count: number;
  verified_purchase: boolean;
}

interface UGCBlockProps {
  dealId: number;
  dealTitle: string;
}

export const UGCBlock: React.FC<UGCBlockProps> = ({ dealId, dealTitle }) => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState(false);
  const [averageRating, setAverageRating] = useState(0);
  const [totalReviews, setTotalReviews] = useState(0);

  useEffect(() => {
    fetchReviews();
  }, [dealId]);

  const fetchReviews = async () => {
    try {
      // For now, generate mock data since UGC table doesn't exist yet
      // In production, this would fetch from Supabase reviews table
      const mockReviews: Review[] = [
        {
          id: 1,
          user_name: "VapeEnthusiast92",
          rating: 5,
          comment: "Amazing deal! Got my order quickly and the product quality is excellent. This coupon saved me $15!",
          created_at: "2025-06-10T10:30:00Z",
          helpful_count: 12,
          verified_purchase: true
        },
        {
          id: 2,
          user_name: "CloudChaser",
          rating: 4,
          comment: "Good discount, though shipping took a bit longer than expected. Product arrived in perfect condition.",
          created_at: "2025-06-08T15:45:00Z",
          helpful_count: 8,
          verified_purchase: true
        },
        {
          id: 3,
          user_name: "ModMaster",
          rating: 5,
          comment: "Best price I've found for this product. The coupon worked perfectly at checkout. Highly recommend!",
          created_at: "2025-06-05T09:20:00Z",
          helpful_count: 15,
          verified_purchase: false
        }
      ];

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setReviews(mockReviews);
      setTotalReviews(mockReviews.length);
      
      if (mockReviews.length > 0) {
        const avgRating = mockReviews.reduce((sum, review) => sum + review.rating, 0) / mockReviews.length;
        setAverageRating(Math.round(avgRating * 10) / 10);
      }
      
    } catch (error) {
      console.error('Error fetching reviews:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric' 
    });
  };

  const renderStars = (rating: number, size: 'sm' | 'md' = 'sm') => {
    const sizeClass = size === 'sm' ? 'w-4 h-4' : 'w-5 h-5';
    
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`${sizeClass} ${
              star <= rating 
                ? 'fill-yellow-400 text-yellow-400' 
                : 'text-design-muted-foreground'
            }`}
          />
        ))}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="bg-design-card border border-design-border rounded-xl p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-design-muted/20 rounded w-48 mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="border-b border-design-border pb-4">
                <div className="h-4 bg-design-muted/20 rounded w-32 mb-2"></div>
                <div className="h-4 bg-design-muted/20 rounded w-full mb-2"></div>
                <div className="h-4 bg-design-muted/20 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (reviews.length === 0) {
    return (
      <div className="bg-design-card border border-design-border rounded-xl p-6">
        <h3 className="text-xl font-semibold text-design-foreground mb-4 flex items-center gap-2">
          <MessageCircle className="w-5 h-5" />
          Customer Reviews
        </h3>
        <div className="text-center py-8">
          <MessageCircle className="w-12 h-12 text-design-muted-foreground mx-auto mb-4" />
          <p className="text-design-muted-foreground">No reviews yet. Be the first to share your experience!</p>
        </div>
      </div>
    );
  }

  const displayedReviews = expanded ? reviews : reviews.slice(0, 2);

  return (
    <div className="bg-design-card border border-design-border rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-design-foreground flex items-center gap-2">
          <MessageCircle className="w-5 h-5" />
          Customer Reviews
        </h3>
        
        {/* Rating Summary */}
        <div className="flex items-center gap-3">
          <div className="text-right">
            <div className="flex items-center gap-2">
              {renderStars(averageRating, 'md')}
              <span className="text-lg font-semibold text-design-foreground">
                {averageRating}
              </span>
            </div>
            <p className="text-sm text-design-muted-foreground">
              {totalReviews} review{totalReviews !== 1 ? 's' : ''}
            </p>
          </div>
        </div>
      </div>

      {/* Reviews List */}
      <div className="space-y-6">
        {displayedReviews.map((review) => (
          <div key={review.id} className="border-b border-design-border pb-6 last:border-b-0 last:pb-0">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                  <span className="text-primary font-semibold text-sm">
                    {review.user_name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium text-design-foreground">
                      {review.user_name}
                    </span>
                    {review.verified_purchase && (
                      <span className="text-xs bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 px-2 py-1 rounded-full">
                        Verified Purchase
                      </span>
                    )}
                  </div>
                  <div className="flex items-center gap-2 mt-1">
                    {renderStars(review.rating)}
                    <span className="text-sm text-design-muted-foreground">
                      {formatDate(review.created_at)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            
            <p className="text-design-foreground mb-3 leading-relaxed">
              {review.comment}
            </p>
            
            <div className="flex items-center gap-4 text-sm">
              <button className="flex items-center gap-1 text-design-muted-foreground hover:text-design-foreground transition-colors">
                <ThumbsUp className="w-4 h-4" />
                <span>Helpful ({review.helpful_count})</span>
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Expand/Collapse Button */}
      {reviews.length > 2 && (
        <div className="mt-6 pt-6 border-t border-design-border">
          <button
            onClick={() => setExpanded(!expanded)}
            className="flex items-center gap-2 text-primary hover:text-primary/80 transition-colors mx-auto"
          >
            {expanded ? (
              <>
                <ChevronUp className="w-4 h-4" />
                Show Less Reviews
              </>
            ) : (
              <>
                <ChevronDown className="w-4 h-4" />
                Show {reviews.length - 2} More Reviews
              </>
            )}
          </button>
        </div>
      )}

      {/* SEO Content */}
      <div className="mt-6 pt-6 border-t border-design-border">
        <div className="text-sm text-design-muted-foreground">
          <p>
            <strong>{dealTitle}</strong> has received {totalReviews} customer reviews with an average rating of {averageRating} out of 5 stars. 
            Customers appreciate the quality and value of this vaping deal. 
            {averageRating >= 4.5 && " This highly-rated coupon is recommended by the vaping community."}
          </p>
        </div>
      </div>
    </div>
  );
};

export default UGCBlock;
