// src/middleware/index.ts
// Comprehensive middleware to prevent all CSS 404 errors and add SEO headers

import { headersMiddleware } from './headers';

export const onRequest = async (context: any, next: () => Promise<Response>) => {
  const url = new URL(context.request.url);
  const pathname = url.pathname;
  
  // Catch all CSS 404 patterns we've observed
  if (
    // Excluded directory patterns
    pathname.includes('/assets/excluded/') ||
    
    // Direct CSS references without proper paths
    (pathname.endsWith('.css') && !pathname.startsWith('/assets/')) ||
    
    // Specific problem files we've identified
    pathname.includes('README.css') ||
    pathname.includes('index.css') ||
    pathname.includes('about-RL_aeLNl.css')
  ) {
    console.log(`[Middleware] Intercepting CSS 404 request: ${pathname}`);
    
    // Return an empty CSS file with appropriate headers
    return new Response('/* Empty CSS file provided by middleware */', {
      status: 200,
      headers: {
        'Content-Type': 'text/css',
        'Cache-Control': 'public, max-age=604800', // 1 week cache
        'X-Content-Type-Options': 'nosniff'
      }
    });
  }
  
  // For all other requests, apply headers middleware and continue
  return headersMiddleware(context, next);
};
