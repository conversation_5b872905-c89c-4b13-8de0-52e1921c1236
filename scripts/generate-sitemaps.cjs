/**
 * Siloed Sitemaps Generator
 * 
 * Generates structured XML sitemaps split by content type:
 * - /sitemap-deals.xml
 * - /sitemap-brands.xml  
 * - /sitemap-merchants.xml
 * - /sitemap-categories.xml
 * - /sitemap_index.xml
 */

const { writeFileSync, mkdirSync } = require('fs');
const { join } = require('path');
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://zlnvivfgzgcjuspktadj.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || '';
const BASE_URL = 'https://vapehybrid.com';
const OUTPUT_DIR = './public';

console.log('SUPABASE_URL:', SUPABASE_URL);
console.log('SUPABASE_SERVICE_KEY exists:', !!SUPABASE_SERVICE_KEY);

// SitemapEntry structure:
// {
//   url: string,
//   lastmod?: string,
//   changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never',
//   priority?: number,
//   images?: Array<{
//     loc: string,
//     title?: string,
//     caption?: string
//   }>
// }

/**
 * Generate XML for a sitemap
 */
function generateSitemapXML(entries) {
  const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>';
  const urlsetOpen = '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">';
  const urlsetClose = '</urlset>';

  const urls = entries.map(entry => {
    let urlXml = `  <url>
    <loc>${entry.url}</loc>`;
    
    if (entry.lastmod) {
      urlXml += `
    <lastmod>${entry.lastmod}</lastmod>`;
    }
    
    if (entry.changefreq) {
      urlXml += `
    <changefreq>${entry.changefreq}</changefreq>`;
    }
    
    if (entry.priority) {
      urlXml += `
    <priority>${entry.priority}</priority>`;
    }

    // Add image entries
    if (entry.images && entry.images.length > 0) {
      entry.images.forEach(image => {
        urlXml += `
    <image:image>
      <image:loc>${image.loc}</image:loc>`;
        if (image.title) {
          urlXml += `
      <image:title>${escapeXml(image.title)}</image:title>`;
        }
        if (image.caption) {
          urlXml += `
      <image:caption>${escapeXml(image.caption)}</image:caption>`;
        }
        urlXml += `
    </image:image>`;
      });
    }

    urlXml += `
  </url>`;
    return urlXml;
  }).join('\n');

  return `${xmlHeader}\n${urlsetOpen}\n${urls}\n${urlsetClose}`;
}

/**
 * Generate sitemap index XML
 */
function generateSitemapIndexXML(sitemaps) {
  const xmlHeader = '<?xml version="1.0" encoding="UTF-8"?>';
  const indexOpen = '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
  const indexClose = '</sitemapindex>';

  const sitemapEntries = sitemaps.map(sitemap => `  <sitemap>
    <loc>${sitemap.loc}</loc>
    <lastmod>${sitemap.lastmod}</lastmod>
  </sitemap>`).join('\n');

  return `${xmlHeader}\n${indexOpen}\n${sitemapEntries}\n${indexClose}`;
}

/**
 * Escape XML special characters
 */
function escapeXml(text) {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
}

/**
 * Generate deals sitemap
 */
async function generateDealsSitemap(supabase) {
  console.log('Generating deals sitemap...');
  
  const { data: deals, error } = await supabase
    .from('deals')
    .select('id, slug, normalized_title, cleaned_title, title, updated_at, imagebig_url, image_url')
    .order('created_at', { ascending: false })
    .limit(5000);

  if (error) {
    console.error('Error fetching deals:', error);
    return [];
  }

  return deals.map((deal) => {
    const slug = deal.slug || deal.normalized_title || deal.cleaned_title || deal.id;
    const images = [];
    
    if (deal.imagebig_url) {
      images.push({
        loc: deal.imagebig_url,
        title: deal.title,
        caption: `${deal.title} - Vape Coupon Deal`
      });
    } else if (deal.image_url) {
      images.push({
        loc: deal.image_url,
        title: deal.title,
        caption: `${deal.title} - Vape Coupon Deal`
      });
    }

    return {
      url: `${BASE_URL}/coupon/${slug}`,
      lastmod: deal.updated_at ? new Date(deal.updated_at).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      changefreq: 'weekly',
      priority: 0.8,
      images: images.length > 0 ? images : undefined
    };
  });
}

/**
 * Generate brands sitemap
 */
async function generateBrandsSitemap(supabase) {
  console.log('Generating brands sitemap...');
  
  const { data: brands, error } = await supabase
    .from('brands')
    .select('id, slug, name, updated_at, logo_url')
    .limit(1000);

  if (error) {
    console.error('Error fetching brands:', error);
    return [];
  }

  return brands.map((brand) => {
    const images = [];
    
    if (brand.logo_url) {
      images.push({
        loc: brand.logo_url,
        title: `${brand.name} Logo`,
        caption: `${brand.name} - Vape Brand Logo`
      });
    }

    return {
      url: `${BASE_URL}/coupons/brands/${brand.slug || brand.id}`,
      lastmod: brand.updated_at ? new Date(brand.updated_at).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      changefreq: 'weekly',
      priority: 0.7,
      images: images.length > 0 ? images : undefined
    };
  });
}

/**
 * Generate merchants sitemap
 */
async function generateMerchantsSitemap(supabase) {
  console.log('Generating merchants sitemap...');
  
  const { data: merchants, error } = await supabase
    .from('merchants')
    .select('id, slug, name, updated_at, logo_url')
    .limit(1000);

  if (error) {
    console.error('Error fetching merchants:', error);
    return [];
  }

  return merchants.map((merchant) => {
    const images = [];
    
    if (merchant.logo_url) {
      images.push({
        loc: merchant.logo_url,
        title: `${merchant.name} Logo`,
        caption: `${merchant.name} - Vape Store Logo`
      });
    }

    return {
      url: `${BASE_URL}/coupons/merchants/${merchant.slug || merchant.id}`,
      lastmod: merchant.updated_at ? new Date(merchant.updated_at).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      changefreq: 'weekly',
      priority: 0.7,
      images: images.length > 0 ? images : undefined
    };
  });
}

/**
 * Generate categories sitemap
 */
async function generateCategoriesSitemap(supabase) {
  console.log('Generating categories sitemap...');
  
  const { data: categories, error } = await supabase
    .from('categories')
    .select('id, slug, name, updated_at, category_logo')
    .limit(500);

  if (error) {
    console.error('Error fetching categories:', error);
    return [];
  }

  return categories.map((category) => {
    const images = [];
    
    if (category.category_logo) {
      images.push({
        loc: category.category_logo,
        title: `${category.name} Category`,
        caption: `${category.name} - Vape Category`
      });
    }

    return {
      url: `${BASE_URL}/coupons/categories/${category.slug || category.id}`,
      lastmod: category.updated_at ? new Date(category.updated_at).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      changefreq: 'weekly',
      priority: 0.7,
      images: images.length > 0 ? images : undefined
    };
  });
}

/**
 * Main function to generate all sitemaps
 */
async function generateAllSitemaps() {
  try {
    console.log('Starting sitemap generation...');
    
    // Initialize Supabase client
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
    
    // Ensure output directory exists
    mkdirSync(OUTPUT_DIR, { recursive: true });
    
    const currentDate = new Date().toISOString().split('T')[0];
    const sitemapIndex = [];

    // Generate individual sitemaps
    const [dealsEntries, brandsEntries, merchantsEntries, categoriesEntries] = await Promise.all([
      generateDealsSitemap(supabase),
      generateBrandsSitemap(supabase),
      generateMerchantsSitemap(supabase),
      generateCategoriesSitemap(supabase)
    ]);

    // Write deals sitemap
    if (dealsEntries.length > 0) {
      const dealsXML = generateSitemapXML(dealsEntries);
      writeFileSync(join(OUTPUT_DIR, 'sitemap-deals.xml'), dealsXML);
      sitemapIndex.push({ loc: `${BASE_URL}/sitemap-deals.xml`, lastmod: currentDate });
      console.log(`✅ Generated sitemap-deals.xml with ${dealsEntries.length} entries`);
    }

    // Write brands sitemap
    if (brandsEntries.length > 0) {
      const brandsXML = generateSitemapXML(brandsEntries);
      writeFileSync(join(OUTPUT_DIR, 'sitemap-brands.xml'), brandsXML);
      sitemapIndex.push({ loc: `${BASE_URL}/sitemap-brands.xml`, lastmod: currentDate });
      console.log(`✅ Generated sitemap-brands.xml with ${brandsEntries.length} entries`);
    }

    // Write merchants sitemap
    if (merchantsEntries.length > 0) {
      const merchantsXML = generateSitemapXML(merchantsEntries);
      writeFileSync(join(OUTPUT_DIR, 'sitemap-merchants.xml'), merchantsXML);
      sitemapIndex.push({ loc: `${BASE_URL}/sitemap-merchants.xml`, lastmod: currentDate });
      console.log(`✅ Generated sitemap-merchants.xml with ${merchantsEntries.length} entries`);
    }

    // Write categories sitemap
    if (categoriesEntries.length > 0) {
      const categoriesXML = generateSitemapXML(categoriesEntries);
      writeFileSync(join(OUTPUT_DIR, 'sitemap-categories.xml'), categoriesXML);
      sitemapIndex.push({ loc: `${BASE_URL}/sitemap-categories.xml`, lastmod: currentDate });
      console.log(`✅ Generated sitemap-categories.xml with ${categoriesEntries.length} entries`);
    }

    // Generate sitemap index
    const indexXML = generateSitemapIndexXML(sitemapIndex);
    writeFileSync(join(OUTPUT_DIR, 'sitemap_index.xml'), indexXML);
    console.log(`✅ Generated sitemap_index.xml with ${sitemapIndex.length} sitemaps`);

    console.log('🎉 All sitemaps generated successfully!');
    
  } catch (error) {
    console.error('❌ Error generating sitemaps:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  generateAllSitemaps();
}

module.exports = { generateAllSitemaps };
