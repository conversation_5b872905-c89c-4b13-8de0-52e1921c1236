/**
 * Coupon Slug Validator
 * 
 * Validates that all coupon/[slug] pages have corresponding database entries
 * Runs during build to catch broken links before deployment
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Configuration
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://zlnvivfgzgcjuspktadj.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || '';

/**
 * Get all valid coupon slugs from database
 */
async function getValidSlugs() {
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
  
  const { data: deals, error } = await supabase
    .from('deals')
    .select('id, slug, normalized_title, cleaned_title, title');

  if (error) {
    throw new Error(`Failed to fetch deals: ${error.message}`);
  }

  const validSlugs = new Set();
  
  deals.forEach(deal => {
    // Add all possible slug variations
    if (deal.slug) validSlugs.add(deal.slug);
    if (deal.normalized_title) validSlugs.add(deal.normalized_title);
    if (deal.cleaned_title) validSlugs.add(deal.cleaned_title);
    validSlugs.add(deal.id.toString()); // ID as fallback
  });

  return { validSlugs, totalDeals: deals.length };
}

/**
 * Validate a specific slug
 */
async function validateSlug(slug) {
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
  
  // Try to find deal by slug field first
  let { data: deal } = await supabase
    .from('deals')
    .select('id, slug, normalized_title, cleaned_title')
    .eq('slug', slug)
    .single();

  if (deal) return { valid: true, dealId: deal.id, matchType: 'slug' };

  // Try normalized_title
  ({ data: deal } = await supabase
    .from('deals')
    .select('id, slug, normalized_title, cleaned_title')
    .eq('normalized_title', slug)
    .single());

  if (deal) return { valid: true, dealId: deal.id, matchType: 'normalized_title' };

  // Try cleaned_title
  ({ data: deal } = await supabase
    .from('deals')
    .select('id, slug, normalized_title, cleaned_title')
    .eq('cleaned_title', slug)
    .single());

  if (deal) return { valid: true, dealId: deal.id, matchType: 'cleaned_title' };

  // Try as ID if numeric
  if (!isNaN(Number(slug))) {
    ({ data: deal } = await supabase
      .from('deals')
      .select('id, slug, normalized_title, cleaned_title')
      .eq('id', parseInt(slug))
      .single());

    if (deal) return { valid: true, dealId: deal.id, matchType: 'id' };
  }

  return { valid: false, dealId: null, matchType: null };
}

/**
 * Generate redirect suggestions for invalid slugs
 */
async function generateRedirectSuggestions(invalidSlug) {
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);
  
  // Look for similar titles
  const { data: similarDeals } = await supabase
    .from('deals')
    .select('id, slug, title, normalized_title')
    .ilike('title', `%${invalidSlug.replace(/-/g, ' ')}%`)
    .limit(3);

  return similarDeals || [];
}

/**
 * Main validation function
 */
async function validateCouponSlugs(slugsToValidate = []) {
  console.log('🔍 Starting coupon slug validation...');
  
  try {
    const { validSlugs, totalDeals } = await getValidSlugs();
    console.log(`📊 Found ${validSlugs.size} valid slugs from ${totalDeals} deals`);

    const results = {
      valid: [],
      invalid: [],
      suggestions: {}
    };

    // If no specific slugs provided, validate a sample
    if (slugsToValidate.length === 0) {
      console.log('ℹ️  No specific slugs provided, validating sample...');
      return results;
    }

    // Validate each provided slug
    for (const slug of slugsToValidate) {
      const result = await validateSlug(slug);
      
      if (result.valid) {
        results.valid.push({
          slug,
          dealId: result.dealId,
          matchType: result.matchType
        });
        console.log(`✅ Valid: ${slug} (matched via ${result.matchType})`);
      } else {
        results.invalid.push(slug);
        console.log(`❌ Invalid: ${slug}`);
        
        // Generate suggestions for invalid slugs
        const suggestions = await generateRedirectSuggestions(slug);
        if (suggestions.length > 0) {
          results.suggestions[slug] = suggestions;
          console.log(`💡 Suggestions for ${slug}:`, suggestions.map(s => s.slug || s.id).join(', '));
        }
      }
    }

    // Summary
    console.log('\n📋 Validation Summary:');
    console.log(`✅ Valid slugs: ${results.valid.length}`);
    console.log(`❌ Invalid slugs: ${results.invalid.length}`);
    
    if (results.invalid.length > 0) {
      console.log('\n🚨 Invalid slugs found:');
      results.invalid.forEach(slug => console.log(`  - ${slug}`));
      
      if (process.env.NODE_ENV === 'production') {
        throw new Error(`Build failed: ${results.invalid.length} invalid coupon slugs found`);
      }
    }

    return results;

  } catch (error) {
    console.error('❌ Slug validation failed:', error);
    throw error;
  }
}

/**
 * CLI interface
 */
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('Usage: node validate-coupon-slugs.cjs [slug1] [slug2] ...');
    console.log('Example: node validate-coupon-slugs.cjs "vape-deal-123" "another-slug"');
    process.exit(0);
  }

  validateCouponSlugs(args)
    .then(results => {
      if (results.invalid.length === 0) {
        console.log('\n🎉 All slugs are valid!');
        process.exit(0);
      } else {
        console.log('\n💥 Some slugs are invalid. Check output above.');
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Validation failed:', error.message);
      process.exit(1);
    });
}

module.exports = { validateCouponSlugs, validateSlug, getValidSlugs };
